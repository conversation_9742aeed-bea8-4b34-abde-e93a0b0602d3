// Hexagonal Nanowire Face Analyzer - Compact Version
// Configuration
ANGLE_TOL = 15; PARALLEL_TOL = 8; MIN_SIZE = 250; SHARP_THRESH = 0.8; MIN_CORNERS = 4;
REG_WEIGHT_REST = 0.4; REG_WEIGHT_FREE = 0.15; OUTLIER_TOL = 0.02;
pixels = 603; 
microns = 500; 
pixelsPerMicron = pixels/microns;

print("\\Clear"); print("=== Hexagonal Nanowire Analyzer ===");
if (nImages == 0) { showMessage("Error", "No image open"); exit(); }
original = getImageID();

// Face Detection
selectImage(original); run("8-bit"); run("Duplicate...", "title=Detection"); detectionID = getImageID();
run("Enhance Contrast", "saturated=0.35"); run("Auto Threshold", "method=MaxEntropy white");
run("Fill Holes"); run("Open"); run("Close-"); run("Gaussian Blur...", "sigma=0.8");
run("Make Binary");
run("Set Measurements...", "area centroid bounding redirect=None decimal=3");
run("Analyze Particles...", "size=" + MIN_SIZE*pixelsPerMicron*pixelsPerMicron + "-Infinity show=Overlay add display exclude clear");
nFaces = nResults; print("Detected " + nFaces + " faces");
if (nFaces == 0) { showMessage("No Faces", "No faces detected"); exit(); }

// User Selection
selectImage(original); roiManager("Show All with labels");
waitForUser("Face Selection", "Select ROI around hexagonal face");
if (selectionType() == -1) { showMessage("Error", "No ROI selected"); exit(); }
originalArea = getValue("Area"); run("Remove Overlay"); roiManager("Reset");
roiManager("Add"); roiManager("Select", 0); roiManager("Rename", "Original_Selection");

// Hexagon Fitting
getSelectionCoordinates(xCoords, yCoords);
if (xCoords.length < 6) { showMessage("Error", "Too few points"); exit(); }

// Get proper centroid using built-in measurements
run("Set Measurements...", "centroid redirect=None decimal=3");
run("Measure"); centroidX = getResult("XM", nResults-1); centroidY = getResult("YM", nResults-1);
run("Clear Results");

run("Convex Hull"); getSelectionCoordinates(hullX, hullY); nHullPoints = hullX.length;
print("Convex hull has " + nHullPoints + " points");
angles = newArray(nHullPoints); distances = newArray(nHullPoints);
for (i = 0; i < nHullPoints; i++) {
    dx = hullX[i] - centroidX; dy = hullY[i] - centroidY;
    angles[i] = atan2(dy, dx) * 180 / PI; if (angles[i] < 0) angles[i] += 360;
    distances[i] = sqrt(dx*dx + dy*dy);
}

// Sort by angle
for (i = 0; i < nHullPoints - 1; i++) {
    for (j = i + 1; j < nHullPoints; j++) {
        if (angles[i] > angles[j]) {
            temp = angles[i]; angles[i] = angles[j]; angles[j] = temp;
            temp = hullX[i]; hullX[i] = hullX[j]; hullX[j] = temp;
            temp = hullY[i]; hullY[i] = hullY[j]; hullY[j] = temp;
            temp = distances[i]; distances[i] = distances[j]; distances[j] = temp;
        }
    }
}

avgRadius = 0; for (i = 0; i < nHullPoints; i++) avgRadius += distances[i]; avgRadius /= nHullPoints;

// Corner sharpness analysis
curvatures = newArray(nHullPoints); CURVATURE_WINDOW = 2;
for (i = 0; i < nHullPoints; i++) {
    prev = (i - CURVATURE_WINDOW + nHullPoints) % nHullPoints;
    next = (i + CURVATURE_WINDOW) % nHullPoints;
    v1x = hullX[i] - hullX[prev]; v1y = hullY[i] - hullY[prev];
    v2x = hullX[next] - hullX[i]; v2y = hullY[next] - hullY[i];
    angle1 = atan2(v1y, v1x); angle2 = atan2(v2y, v2x);
    angleDiff = angle2 - angle1;
    while (angleDiff > PI) angleDiff -= 2 * PI;
    while (angleDiff < -PI) angleDiff += 2 * PI;
    curvatures[i] = abs(angleDiff);
}

// Sorting curvature list

for (i = 0; i < nHullPoints - 1; i++) {
    for (j = i + 1; j < nHullPoints; j++) {
        if (curvatures[i] < curvatures[j]) {
            temp = curvatures[i]; curvatures[i] = curvatures[j]; curvatures[j] = temp;
            temp = hullX[i]; hullX[i] = hullX[j]; hullX[j] = temp;
            temp = hullY[i]; hullY[i] = hullY[j]; hullY[j] = temp;
        }
    }
}

for (i = 0; i < nHullPoints; i++) {
    print("Point " + i + " curvature=" + d2s(curvatures[i], 4));
}

// Identify sharp corners
sharpCornerCount = 0; maxCurvature = 0;
for (i = 0; i < nHullPoints; i++) if (curvatures[i] > maxCurvature) maxCurvature = curvatures[i];
for (i = 0; i < nHullPoints; i++) {
    if (maxCurvature > 0 && curvatures[i] / maxCurvature > SHARP_THRESH) {
        prev = (i - 1 + nHullPoints) % nHullPoints;         // method to find neighbors, even over the start and end
        next = (i + 1) % nHullPoints;
        if (curvatures[i] >= curvatures[prev] && curvatures[i] >= curvatures[next]) sharpCornerCount++;
    }
}

print("Found " + sharpCornerCount + " sharp corners");
useRegularityConstraint = !(sharpCornerCount >= MIN_CORNERS);
if (useRegularityConstraint) currentRegularityWeight = REG_WEIGHT_REST;
else currentRegularityWeight = REG_WEIGHT_FREE;

hexVerticesX = newArray(6); hexVerticesY = newArray(6);

if (useRegularityConstraint) {
    print("Using regularity-weighted approach");
    // Regular hexagon approach
    bestScore = -999999; bestRotation = 0;
    for (testRotation = 0; testRotation < 360; testRotation += 1.0) {
        score = calculateContainmentScore(testRotation, centroidX, centroidY, avgRadius, hullX, hullY, nHullPoints);
        if (score > bestScore) { bestScore = score; bestRotation = testRotation; }
    }
    
    // Fine scan
    for (testRotation = bestRotation - 5; testRotation <= bestRotation + 5; testRotation += 0.1) {
        score = calculateContainmentScore(testRotation, centroidX, centroidY, avgRadius, hullX, hullY, nHullPoints);
        if (score > bestScore) { bestScore = score; bestRotation = testRotation; }
    }
    
    // Generate vertices
    for (i = 0; i < 6; i++) {
        angle = (i * 60 + bestRotation) * PI / 180;
        hexVerticesX[i] = centroidX + avgRadius * cos(angle);
        hexVerticesY[i] = centroidY + avgRadius * sin(angle);
    }
    
    // Expand for containment
    expansion = 0; maxExpansions = 30;
    while (expansion < maxExpansions) {
        outsideCount = 0;
        for (i = 0; i < nHullPoints; i++) {
            if (!isPointInPolygon(hullX[i], hullY[i], hexVerticesX, hexVerticesY, 6)) outsideCount++;
        }
        if (outsideCount / nHullPoints <= OUTLIER_TOL) break;
        for (i = 0; i < 6; i++) {
            dx = hexVerticesX[i] - centroidX; dy = hexVerticesY[i] - centroidY;
            length = sqrt(dx*dx + dy*dy);
            if (length > 0) {
                scale = (length + 0.15) / length;
                hexVerticesX[i] = centroidX + dx * scale;
                hexVerticesY[i] = centroidY + dy * scale;
            }
        }
        expansion++;
    }
} else {
    print("Using adaptive corner-based approach");
    // Adaptive corner-based approach
    allCorners = newArray(); cornerCount = 0;
    for (i = 0; i < nHullPoints; i++) {
        if (curvatures[i] > 0.2) {
            prev = (i - 1 + nHullPoints) % nHullPoints; next = (i + 1) % nHullPoints;
            if (curvatures[i] >= curvatures[prev] && curvatures[i] >= curvatures[next]) {
                allCorners = Array.concat(allCorners, i); cornerCount++;
            }
        }
    }
    
    if (cornerCount >= 6) {
        // Sort by curvature and take top 6
        cornerScores = newArray(cornerCount);
        for (i = 0; i < cornerCount; i++) cornerScores[i] = curvatures[allCorners[i]];
        for (i = 0; i < cornerCount - 1; i++) {
            for (j = i + 1; j < cornerCount; j++) {
                if (cornerScores[i] < cornerScores[j]) {
                    temp = allCorners[i]; allCorners[i] = allCorners[j]; allCorners[j] = temp;
                    temp = cornerScores[i]; cornerScores[i] = cornerScores[j]; cornerScores[j] = temp;
                }
            }
        }
        
        // Sort top 6 by angle
        selectedCorners = newArray(6);
        for (i = 0; i < 6; i++) selectedCorners[i] = allCorners[i];
        for (i = 0; i < 5; i++) {
            for (j = i + 1; j < 6; j++) {
                angle_i = atan2(hullY[selectedCorners[i]] - centroidY, hullX[selectedCorners[i]] - centroidX);
                angle_j = atan2(hullY[selectedCorners[j]] - centroidY, hullX[selectedCorners[j]] - centroidX);
                if (angle_i > angle_j) {
                    temp = selectedCorners[i]; selectedCorners[i] = selectedCorners[j]; selectedCorners[j] = temp;
                }
            }
        }
        
        for (i = 0; i < 6; i++) {
            hexVerticesX[i] = hullX[selectedCorners[i]];
            hexVerticesY[i] = hullY[selectedCorners[i]];
        }
    } else {
        // Sector-based fallback
        for (sector = 0; sector < 6; sector++) {
            sectorStart = sector * 60; sectorEnd = (sector + 1) * 60;
            bestPointIdx = -1; maxDist = 0;
            for (i = 0; i < nHullPoints; i++) {
                angle = angles[i]; if (sector == 5 && angle < 60) angle += 360;
                if (angle >= sectorStart && angle < sectorEnd && distances[i] > maxDist) {
                    maxDist = distances[i]; bestPointIdx = i;
                }
            }
            if (bestPointIdx >= 0) {
                hexVerticesX[sector] = hullX[bestPointIdx];
                hexVerticesY[sector] = hullY[bestPointIdx];
            } else {
                targetAngle = (sectorStart + sectorEnd) / 2 * PI / 180;
                hexVerticesX[sector] = centroidX + avgRadius * cos(targetAngle);
                hexVerticesY[sector] = centroidY + avgRadius * sin(targetAngle);
            }
        }
    }
}

// Multi-stage refinement process
print("Starting multi-stage refinement...");

// Stage 1: Centroid refinement
print("Stage 1: Centroid refinement");
bestCentroidScore = -999999; bestCentroidX = centroidX; bestCentroidY = centroidY;
for (cx = centroidX - 3; cx <= centroidX + 3; cx += 0.5) {
    for (cy = centroidY - 3; cy <= centroidY + 3; cy += 0.5) {
        if (cx == centroidX && cy == centroidY) continue;

        // Test hexagon with new centroid
        testHexX = newArray(6); testHexY = newArray(6);
        for (i = 0; i < 6; i++) {
            dx = hexVerticesX[i] - centroidX; dy = hexVerticesY[i] - centroidY;
            testHexX[i] = cx + dx; testHexY[i] = cy + dy;
        }

        score = calculateDirectionalShiftScore(testHexX, testHexY, hullX, hullY, nHullPoints);
        if (score > bestCentroidScore) {
            bestCentroidScore = score; bestCentroidX = cx; bestCentroidY = cy;
        }
    }
}

// Apply best centroid
if (sqrt((bestCentroidX - centroidX) * (bestCentroidX - centroidX) + (bestCentroidY - centroidY) * (bestCentroidY - centroidY)) > 0.1) {
    dx_shift = bestCentroidX - centroidX; dy_shift = bestCentroidY - centroidY;
    for (i = 0; i < 6; i++) { hexVerticesX[i] += dx_shift; hexVerticesY[i] += dy_shift; }
    centroidX = bestCentroidX; centroidY = bestCentroidY;
    print("Centroid refined by " + d2s(sqrt(dx_shift*dx_shift + dy_shift*dy_shift), 2) + " pixels");
}

// Stage 2: Individual vertex mobility (respecting angle constraints)
print("Stage 2: Individual vertex refinement");
for (refinementRound = 0; refinementRound < 3; refinementRound++) {
    for (vertexIdx = 0; vertexIdx < 6; vertexIdx++) {
        bestVertexScore = -999999; bestVertexX = hexVerticesX[vertexIdx]; bestVertexY = hexVerticesY[vertexIdx];

        for (vx = hexVerticesX[vertexIdx] - 2; vx <= hexVerticesX[vertexIdx] + 2; vx += 0.5) {
            for (vy = hexVerticesY[vertexIdx] - 2; vy <= hexVerticesY[vertexIdx] + 2; vy += 0.5) {
                if (vx == hexVerticesX[vertexIdx] && vy == hexVerticesY[vertexIdx]) continue;

                // Test vertex position
                testHexX = Array.copy(hexVerticesX); testHexY = Array.copy(hexVerticesY);
                testHexX[vertexIdx] = vx; testHexY[vertexIdx] = vy;

                // Check angle constraints
                if (!isAngleConstraintViolated(testHexX, testHexY, vertexIdx)) {
                    score = calculateDirectionalShiftScore(testHexX, testHexY, hullX, hullY, nHullPoints);
                    if (score > bestVertexScore) {
                        bestVertexScore = score; bestVertexX = vx; bestVertexY = vy;
                    }
                }
            }
        }

        // Apply best vertex position
        if (sqrt((bestVertexX - hexVerticesX[vertexIdx]) * (bestVertexX - hexVerticesX[vertexIdx]) +
                (bestVertexY - hexVerticesY[vertexIdx]) * (bestVertexY - hexVerticesY[vertexIdx])) > 0.1) {
            hexVerticesX[vertexIdx] = bestVertexX; hexVerticesY[vertexIdx] = bestVertexY;
        }
    }
}

// Stage 3: Directional optimization
print("Stage 3: Directional optimization");
side1X = hexVerticesX[1] - hexVerticesX[0]; side1Y = hexVerticesY[1] - hexVerticesY[0];
baseOrientation = atan2(side1Y, side1X) * 180 / PI;

for (directionRound = 0; directionRound < 2; directionRound++) {
    bestFinalScore = -999999; bestShiftX = 0; bestShiftY = 0;

    for (dirIdx = 0; dirIdx < 12; dirIdx++) {
        if (dirIdx < 6) {
            direction = (baseOrientation + dirIdx * 60) * PI / 180;
        }
        else {
            direction = (baseOrientation + (dirIdx - 6) * 60 + 30) * PI / 180;
        }
        directionX = cos(direction); directionY = sin(direction);

        if (directionRound == 0) shiftRange = 3.0; else shiftRange = 1.5;   // Coarse then fine
        if (directionRound == 0) shiftStep = 0.5; else shiftStep = 0.25;
        
        for (shiftMagnitude = -shiftRange; shiftMagnitude <= shiftRange; shiftMagnitude += shiftStep) {
            if (shiftMagnitude == 0) continue;
            shiftX = shiftMagnitude * directionX; shiftY = shiftMagnitude * directionY;

            testHexX = newArray(6); testHexY = newArray(6);
            for (i = 0; i < 6; i++) { testHexX[i] = hexVerticesX[i] + shiftX; testHexY[i] = hexVerticesY[i] + shiftY; }

            score = calculateDirectionalShiftScore(testHexX, testHexY, hullX, hullY, nHullPoints);
            if (score > bestFinalScore) { bestFinalScore = score; bestShiftX = shiftX; bestShiftY = shiftY; }
        }
    }

    // Apply best shift for this round
    if (sqrt(bestShiftX * bestShiftX + bestShiftY * bestShiftY) > 0.1) {
        for (i = 0; i < 6; i++) { hexVerticesX[i] += bestShiftX; hexVerticesY[i] += bestShiftY; }
        centroidX += bestShiftX; centroidY += bestShiftY;
        print("Direction round " + (directionRound + 1) + ": shifted " + d2s(sqrt(bestShiftX*bestShiftX + bestShiftY*bestShiftY), 2) + " pixels");
    }
}

// Create fitted hexagon and validate
makeSelection("polygon", hexVerticesX, hexVerticesY); fittedArea = getValue("Area");
validHexagon = true;

// Angle validation
for (i = 0; i < 6; i++) {
    p1 = i; p2 = (i + 1) % 6; p3 = (i + 2) % 6;
    v1x = hexVerticesX[p1] - hexVerticesX[p2]; v1y = hexVerticesY[p1] - hexVerticesY[p2];
    v2x = hexVerticesX[p3] - hexVerticesX[p2]; v2y = hexVerticesY[p3] - hexVerticesY[p2];
    dot = v1x * v2x + v1y * v2y; mag1 = sqrt(v1x * v1x + v1y * v1y); mag2 = sqrt(v2x * v2x + v2y * v2y);
    if (mag1 > 0 && mag2 > 0) {
        cosAngle = dot / (mag1 * mag2); if (cosAngle > 1) cosAngle = 1; if (cosAngle < -1) cosAngle = -1;
        angle = acos(cosAngle) * 180 / PI; angleError = abs(angle - 120);
        if (angleError > ANGLE_TOL) validHexagon = false;
    }
}

// Parallel line validation
for (i = 0; i < 3; i++) {
    side1_start = i; side1_end = (i + 1) % 6; side2_start = (i + 3) % 6; side2_end = (i + 4) % 6;
    dx1 = hexVerticesX[side1_end] - hexVerticesX[side1_start]; dy1 = hexVerticesY[side1_end] - hexVerticesY[side1_start];
    dx2 = hexVerticesX[side2_end] - hexVerticesX[side2_start]; dy2 = hexVerticesY[side2_end] - hexVerticesY[side2_start];
    angle1 = atan2(dy1, dx1) * 180 / PI; angle2 = atan2(dy2, dx2) * 180 / PI;
    if (angle1 < 0) angle1 += 180; if (angle2 < 0) angle2 += 180;
    angleDiff = abs(angle1 - angle2); if (angleDiff > 90) angleDiff = 180 - angleDiff;
    if (angleDiff > PARALLEL_TOL) validHexagon = false;
}

// Results
areaDifference = abs(fittedArea - originalArea); areaRatio = fittedArea / originalArea;
print("Original: " + d2s(originalArea, 2) + " | Fitted: " + d2s(fittedArea, 2) + " | Ratio: " + d2s(areaRatio, 3));
if (validHexagon) {
    print("Hexagon validation: PASSED");
}
else {
    print("Hexagon validation: FAILED");
}

// Cleanup and display
run("Remove Overlay"); roiManager("Reset"); roiManager("Add"); roiManager("Select", 0);
roiManager("Rename", "Fitted_Hexagon"); roiManager("Show All with labels"); roiManager("Deselect");
selectImage(detectionID); selectImage(original);
print("=== Analysis Complete ===");

// Helper Functions
function calculateContainmentScore(rotation, centerX, centerY, radius, hullPointsX, hullPointsY, numPoints) {
    verticesX = newArray(6); verticesY = newArray(6);
    for (i = 0; i < 6; i++) {
        angle = (i * 60 + rotation) * PI / 180;
        verticesX[i] = centerX + radius * cos(angle); verticesY[i] = centerY + radius * sin(angle);
    }
    insideCount = 0; totalFitDistance = 0;
    for (i = 0; i < numPoints; i++) {
        if (isPointInPolygon(hullPointsX[i], hullPointsY[i], verticesX, verticesY, 6)) insideCount++;
        minVertexDist = 999999;
        for (j = 0; j < 6; j++) {
            dist = sqrt((hullPointsX[i] - verticesX[j]) * (hullPointsX[i] - verticesX[j]) +
                       (hullPointsY[i] - verticesY[j]) * (hullPointsY[i] - verticesY[j]));
            if (dist < minVertexDist) minVertexDist = sqrt(sqrt(dist));       // experimental: sqrt to reduce the impact of pixels in the inside
        }
        totalFitDistance += minVertexDist;
    }
    IJ.log("insideCount/numPoints: " + insideCount/numPoints + "  totalFitDistance/numPoints: " + totalFitDistance/numPoints);
    return (insideCount / numPoints) * 1000 - (totalFitDistance / numPoints) * 100;
}

function isPointInPolygon(px, py, polyX, polyY, numVertices) {
    inside = false; j = numVertices - 1;
    for (i = 0; i < numVertices; i++) {
        if (((polyY[i] > py) != (polyY[j] > py)) && (px < (polyX[j] - polyX[i]) * (py - polyY[i]) / (polyY[j] - polyY[i]) + polyX[i]))
            inside = !inside;
        j = i;
    }
    return inside;
}

function calculateDirectionalShiftScore(testVerticesX, testVerticesY, hullPointsX, hullPointsY, numPoints) {
    insideCount = 0;
    for (i = 0; i < numPoints; i++) {
        if (isPointInPolygon(hullPointsX[i], hullPointsY[i], testVerticesX, testVerticesY, 6)) insideCount++;
    }
    return insideCount / numPoints * 1000;
}

function isAngleConstraintViolated(verticesX, verticesY, changedVertexIdx) {
    MAX_ANGLE_DEVIATION = 20; // Degrees tolerance for individual vertex movement

    for (i = 0; i < 6; i++) {
        prev = (i - 1 + 6) % 6; next = (i + 1) % 6;
        v1x = verticesX[prev] - verticesX[i]; v1y = verticesY[prev] - verticesY[i];
        v2x = verticesX[next] - verticesX[i]; v2y = verticesY[next] - verticesY[i];

        angle1 = atan2(v1y, v1x); angle2 = atan2(v2y, v2x);
        angleDiff = angle2 - angle1;
        while (angleDiff < 0) angleDiff += 2 * PI;
        while (angleDiff >= 2 * PI) angleDiff -= 2 * PI;

        interiorAngle = angleDiff * 180 / PI;
        if (interiorAngle > 180) interiorAngle = 360 - interiorAngle;

        angleError = abs(interiorAngle - 120);
        if (angleError > MAX_ANGLE_DEVIATION) return true;
    }
    return false;
}
