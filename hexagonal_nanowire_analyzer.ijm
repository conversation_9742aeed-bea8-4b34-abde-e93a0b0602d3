/*
 * Hexagonal Nanowire Face Analyzer
 * 
 * This script detects hexagonal nanowire faces (bright spots) and allows the user
 * to select a ROI that is then fitted with 6 lines forming a hexagon.
 * The angles between neighboring lines can deviate from perfect 120° (±20° tolerance).
 * Opposing lines should be approximately parallel (with tolerance).
 * Calculates the difference between the fitted hexagonal area and original ROI area.
 */

// === CONFIGURATION ===
ANGLE_TOLERANCE = 15;  // Degrees tolerance from perfect 120° (tightened)
PARALLEL_TOLERANCE = 8;  // Degrees tolerance for parallel lines (tightened)
MIN_FACE_SIZE = 250;  // Minimum area for face detection
SHOW_INTERMEDIATE_STEPS = true;  // Show detection and fitting steps
REGULARITY_WEIGHT_RESTRICTED = 0.4;  // Weight for regularity vs fit in restricted mode (reduced)
REGULARITY_WEIGHT_FREE = 0.15;     // Weight for regularity vs fit in free mode (light constraint)
DEVIATION_PENALTY_FACTOR = 2.0;    // Factor to penalize deviations more harshly
// New corner sharpness-based classification
CORNER_SHARPNESS_THRESHOLD = 0.8;  // Threshold for corner sharpness (higher = more hexagonal)
MIN_SHARP_CORNERS = 4;             // Minimum number of sharp corners for hexagonal classification
// Precision-focused parameters
BROAD_ROTATION_STEP = 1.0;  // Degrees for initial rotation scanning
FINE_ROTATION_STEP = 0.1;   // Degrees for fine rotation scanning
FINE_SCAN_RANGE = 5.0;      // Degrees range around best orientation for fine scan
OUTLIER_TOLERANCE = 0.02;   // Fraction of points allowed outside hexagon (2%)
CONTAINMENT_EXPANSION_STEP = 0.5;  // Pixel step for hexagon expansion
CENTROID_OPTIMIZATION_RANGE = 3.0;  // Pixel range for centroid optimization
CENTROID_OPTIMIZATION_STEP = 0.5;   // Pixel step for centroid optimization

// === INITIALIZATION ===
print("\\Clear");
print("=== Hexagonal Nanowire Face Analyzer ===");
print("Initialization...");

// Get current image
if (nImages == 0) {
    showMessage("Error", "No image is open. Please open an image first.");
    exit();
}
run("Set Scale...", "distance=603 known=500 unit=µm");
original = getImageID();
originalTitle = getTitle();
print("Processing image: " + originalTitle);

// === FACE DETECTION ===
print("\n--- Face Detection ---");

// Duplicate image for processing
selectImage(original);
run("Duplicate...", "title=Detection");
detectionID = getImageID();

// Enhance contrast to highlight bright faces
run("Enhance Contrast", "saturated=0.35");

// Create binary mask for bright 
run("Auto Threshold", "method=MaxEntropy white");
run("Fill Holes");

// Clean up small artifacts
run("Open");
run("Close-");

// Detect potential hexagonal faces
run("Set Measurements...", "area centroid bounding redirect=None decimal=3");
run("Analyze Particles...", "size=" + MIN_FACE_SIZE + "-Infinity show=Overlay add display exclude clear");

nFaces = nResults;
print("Detected " + nFaces + " potential hexagonal faces");

if (nFaces == 0) {
    showMessage("No Faces Found", "No potential hexagonal faces detected.\nTry adjusting the MIN_FACE_SIZE parameter.");
    exit();
}

// === USER SELECTION ===
print("\n--- User Selection ---");

// Switch back to original image
selectImage(original);
roiManager("Show All with labels");

// Wait for user to select a face
waitForUser("Face Selection", 
    "Detected " + nFaces + " potential faces (shown as overlays).\n \n" +
    "Please select a ROI around the hexagonal face you want to analyze.\n" +
    "You can:\n" +
    "- Draw a new selection around a face\n" +
    "- Select an existing ROI from the ROI Manager\n \n" +
    "Click OK when ready to proceed with hexagon fitting.");

// Get the selected ROI
if (selectionType() == -1) {
    showMessage("Error", "No ROI selected. Please select a region around a hexagonal face.");
    exit();
}

// Store original ROI area and clean up overlays
getSelectionBounds(roiX, roiY, roiWidth, roiHeight);
originalArea = getValue("Area");
print("Selected ROI area: " + originalArea + " pixels²");

// Clean up all existing overlays and ROI Manager entries except the user selection
print("Cleaning up detection overlays...");
run("Remove Overlay");
roiManager("Reset");

// Re-add the user selection to ROI Manager for reference
roiManager("Add");
roiManager("Select", 0);
roiManager("Rename", "Original_Selection");

// === HEXAGON FITTING ===
print("\n--- Hexagon Fitting ---");

// Get ROI coordinates for edge detection
getSelectionCoordinates(xCoords, yCoords);
nPoints = xCoords.length;

if (nPoints < 6) {
    showMessage("Error", "Selected ROI has too few points for hexagon fitting.");
    exit();
}

// Find edge points using convex hull approach
run("Convex Hull");
getSelectionCoordinates(hullX, hullY);
nHullPoints = hullX.length;

print("Convex hull has " + nHullPoints + " points");

// === LINE FITTING ALGORITHM ===
print("Fitting hexagon lines...");

// Calculate centroid of the shape
centroidX = 0;
centroidY = 0;
for (i = 0; i < nHullPoints; i++) {
    centroidX += hullX[i];
    centroidY += hullY[i];
}
centroidX /= nHullPoints;
centroidY /= nHullPoints;

// Convert points to polar coordinates relative to centroid
angles = newArray(nHullPoints);
distances = newArray(nHullPoints);

for (i = 0; i < nHullPoints; i++) {
    dx = hullX[i] - centroidX;
    dy = hullY[i] - centroidY;
    angles[i] = atan2(dy, dx) * 180 / PI;
    if (angles[i] < 0) angles[i] += 360;
    distances[i] = sqrt(dx*dx + dy*dy);
}

// Sort points by angle
for (i = 0; i < nHullPoints - 1; i++) {
    for (j = i + 1; j < nHullPoints; j++) {
        if (angles[i] > angles[j]) {
            // Swap angles
            temp = angles[i];
            angles[i] = angles[j];
            angles[j] = temp;
            // Swap coordinates
            temp = hullX[i];
            hullX[i] = hullX[j];
            hullX[j] = temp;
            temp = hullY[i];
            hullY[i] = hullY[j];
            hullY[j] = temp;
            // Swap distances
            temp = distances[i];
            distances[i] = distances[j];
            distances[j] = temp;
        }
    }
}

// === ADAPTIVE HEXAGON VERTEX DETECTION WITH REGULARITY WEIGHTING ===
print("Detecting hexagon vertices with regularity weighting...");

// Calculate average radius for regular hexagon reference
avgRadius = 0;
for (i = 0; i < nHullPoints; i++) {
    avgRadius += distances[i];
}
avgRadius /= nHullPoints;

// Calculate shape irregularity metric
irregularityScore = 0;
for (i = 0; i < nHullPoints; i++) {
    deviation = abs(distances[i] - avgRadius) / avgRadius;
    irregularityScore += deviation;
}
irregularityScore /= nHullPoints;

print("Shape irregularity score: " + d2s(irregularityScore, 3) + " (0=perfect circle, higher=more irregular)");

// === CORNER SHARPNESS ANALYSIS ===
print("Analyzing corner sharpness for shape classification...");

// Calculate curvature at each hull point to assess corner sharpness
curvatures = newArray(nHullPoints);
CURVATURE_WINDOW = 2;

for (i = 0; i < nHullPoints; i++) {
    prev = (i - CURVATURE_WINDOW + nHullPoints) % nHullPoints;
    next = (i + CURVATURE_WINDOW) % nHullPoints;

    // Calculate vectors
    v1x = hullX[i] - hullX[prev];
    v1y = hullY[i] - hullY[prev];
    v2x = hullX[next] - hullX[i];
    v2y = hullY[next] - hullY[i];

    // Calculate angle change (curvature)
    angle1 = atan2(v1y, v1x);
    angle2 = atan2(v2y, v2x);
    angleDiff = angle2 - angle1;

    // Normalize angle difference to [-π, π]
    while (angleDiff > PI) angleDiff -= 2 * PI;
    while (angleDiff < -PI) angleDiff += 2 * PI;

    curvatures[i] = abs(angleDiff);
}

// Find sharp corners (high curvature peaks)
sharpCorners = newArray();
sharpCornerCount = 0;
maxCurvature = 0;

// Find maximum curvature for normalization
for (i = 0; i < nHullPoints; i++) {
    if (curvatures[i] > maxCurvature) {
        maxCurvature = curvatures[i];
    }
}

// Identify sharp corners
for (i = 0; i < nHullPoints; i++) {
    if (maxCurvature > 0) {
        normalizedCurvature = curvatures[i] / maxCurvature;

        if (normalizedCurvature > CORNER_SHARPNESS_THRESHOLD) {
            // Check if this is a local maximum
            prev = (i - 1 + nHullPoints) % nHullPoints;
            next = (i + 1) % nHullPoints;

            if (curvatures[i] >= curvatures[prev] && curvatures[i] >= curvatures[next]) {
                sharpCorners = Array.concat(sharpCorners, i);
                sharpCornerCount++;
            }
        }
    }
}

// Calculate average corner sharpness
avgSharpness = 0;
if (sharpCornerCount > 0) {
    for (i = 0; i < sharpCornerCount; i++) {
        avgSharpness += curvatures[sharpCorners[i]] / maxCurvature;
    }
    avgSharpness /= sharpCornerCount;
}

print("Found " + sharpCornerCount + " sharp corners (avg sharpness: " + d2s(avgSharpness, 3) + ")");

// Determine fitting strategy based on corner sharpness
if (sharpCornerCount >= MIN_SHARP_CORNERS && avgSharpness > CORNER_SHARPNESS_THRESHOLD) {
    print("Sharp corners detected - using constrained adaptive fitting");
    useRegularityConstraint = false;
    currentRegularityWeight = REGULARITY_WEIGHT_FREE;
} else {
    print("Smooth/circular shape detected - applying regularity constraints");
    useRegularityConstraint = true;
    currentRegularityWeight = REGULARITY_WEIGHT_RESTRICTED;
}

// Generate candidate hexagon vertices
hexVerticesX = newArray(6);
hexVerticesY = newArray(6);

if (useRegularityConstraint) {
    // === HIGH-PRECISION REGULARITY-WEIGHTED APPROACH ===
    print("Using high-precision orientation detection...");

    // STAGE 1: Broad rotation scanning
    bestScore = -999999;
    bestRotation = 0;

    print("Stage 1: Broad scanning (1° steps)...");
    for (testRotation = 0; testRotation < 360; testRotation += BROAD_ROTATION_STEP) {
        score = calculateContainmentScore(testRotation, centroidX, centroidY, avgRadius, hullX, hullY, nHullPoints);
        if (score > bestScore) {
            bestScore = score;
            bestRotation = testRotation;
        }
    }

    // STAGE 2: Fine rotation scanning around best orientation
    print("Stage 2: Fine scanning (0.1° steps) around " + d2s(bestRotation, 1) + "°...");
    fineStart = bestRotation - FINE_SCAN_RANGE;
    fineEnd = bestRotation + FINE_SCAN_RANGE;

    for (testRotation = fineStart; testRotation <= fineEnd; testRotation += FINE_ROTATION_STEP) {
        score = calculateContainmentScore(testRotation, centroidX, centroidY, avgRadius, hullX, hullY, nHullPoints);
        if (score > bestScore) {
            bestScore = score;
            bestRotation = testRotation;
        }
    }

    print("Optimal orientation: " + d2s(bestRotation, 2) + "° (score: " + d2s(bestScore, 1) + ")");

    // STAGE 3: Optimize centroid position to minimize upward shift
    print("Stage 3a: Optimizing centroid position...");

    bestCentroidX = centroidX;
    bestCentroidY = centroidY;
    bestCentroidScore = calculateContainmentScore(bestRotation, centroidX, centroidY, avgRadius, hullX, hullY, nHullPoints);

    // Test different centroid positions in a grid around the original centroid
    for (dx = -CENTROID_OPTIMIZATION_RANGE; dx <= CENTROID_OPTIMIZATION_RANGE; dx += CENTROID_OPTIMIZATION_STEP) {
        for (dy = -CENTROID_OPTIMIZATION_RANGE; dy <= CENTROID_OPTIMIZATION_RANGE; dy += CENTROID_OPTIMIZATION_STEP) {
            if (dx == 0 && dy == 0) continue;

            testCentroidX = centroidX + dx;
            testCentroidY = centroidY + dy;

            // Calculate score for this centroid position
            score = calculateContainmentScore(bestRotation, testCentroidX, testCentroidY, avgRadius, hullX, hullY, nHullPoints);

            if (score > bestCentroidScore) {
                bestCentroidScore = score;
                bestCentroidX = testCentroidX;
                bestCentroidY = testCentroidY;
            }
        }
    }

    // Update centroid if a better position was found
    if (bestCentroidX != centroidX || bestCentroidY != centroidY) {
        print("Centroid optimized: moved by (" + d2s(bestCentroidX - centroidX, 1) + ", " + d2s(bestCentroidY - centroidY, 1) + ") pixels");
        centroidX = bestCentroidX;
        centroidY = bestCentroidY;
    } else {
        print("Centroid position already optimal");
    }

    // STAGE 3b: Generate initial vertices at optimal orientation and centroid
    initialVerticesX = newArray(6);
    initialVerticesY = newArray(6);

    for (i = 0; i < 6; i++) {
        angle = (i * 60 + bestRotation) * PI / 180;
        initialVerticesX[i] = centroidX + avgRadius * cos(angle);
        initialVerticesY[i] = centroidY + avgRadius * sin(angle);
    }

    // STAGE 4: Smart containment optimization (less aggressive expansion)
    print("Stage 3: Smart containment optimization...");
    hexVerticesX = Array.copy(initialVerticesX);
    hexVerticesY = Array.copy(initialVerticesY);

    // First, try minimal expansion with smaller steps
    maxExpansions = 30; // Reduced iterations
    expansion = 0;
    smallExpansionStep = CONTAINMENT_EXPANSION_STEP * 0.3; // Smaller steps

    while (expansion < maxExpansions) {
        // Count points outside current hexagon
        outsideCount = 0;
        for (i = 0; i < nHullPoints; i++) {
            if (!isPointInPolygon(hullX[i], hullY[i], hexVerticesX, hexVerticesY, 6)) {
                outsideCount++;
            }
        }

        outsideFraction = outsideCount / nHullPoints;

        if (outsideFraction <= OUTLIER_TOLERANCE) {
            print("Containment achieved: " + d2s((1-outsideFraction)*100, 1) + "% of points inside");
            break;
        }

        // Expand hexagon with smaller steps
        for (i = 0; i < 6; i++) {
            dx = hexVerticesX[i] - centroidX;
            dy = hexVerticesY[i] - centroidY;
            length = sqrt(dx*dx + dy*dy);
            if (length > 0) {
                scale = (length + smallExpansionStep) / length;
                hexVerticesX[i] = centroidX + dx * scale;
                hexVerticesY[i] = centroidY + dy * scale;
            }
        }

        expansion++;
    }

    if (expansion >= maxExpansions) {
        print("Warning: Maximum expansions reached, " + d2s((1-outsideFraction)*100, 1) + "% containment");
    }

    // STAGE 5: Fine-tune vertices with regularity constraint
    print("Stage 4: Fine-tuning vertices...");
    for (i = 0; i < 6; i++) {
        angle = (i * 60 + bestRotation) * PI / 180;
        regularX = centroidX + avgRadius * cos(angle);
        regularY = centroidY + avgRadius * sin(angle);

        // Find closest hull point but limit adjustment distance
        minDist = 999999;
        bestHullX = hexVerticesX[i]; // Start with current expanded position
        bestHullY = hexVerticesY[i];

        for (j = 0; j < nHullPoints; j++) {
            dist = sqrt((hullX[j] - hexVerticesX[i]) * (hullX[j] - hexVerticesX[i]) +
                       (hullY[j] - hexVerticesY[i]) * (hullY[j] - hexVerticesY[i]));
            if (dist < minDist) {
                minDist = dist;
                bestHullX = hullX[j];
                bestHullY = hullY[j];
            }
        }

        // Apply regularity weighting but preserve containment
        weight = currentRegularityWeight;
        candidateX = weight * regularX + (1 - weight) * bestHullX;
        candidateY = weight * regularY + (1 - weight) * bestHullY;

        // Ensure candidate doesn't reduce containment
        distFromCenter = sqrt((candidateX - centroidX) * (candidateX - centroidX) +
                             (candidateY - centroidY) * (candidateY - centroidY));
        currentDistFromCenter = sqrt((hexVerticesX[i] - centroidX) * (hexVerticesX[i] - centroidX) +
                                    (hexVerticesY[i] - centroidY) * (hexVerticesY[i] - centroidY));

        if (distFromCenter >= currentDistFromCenter * 0.95) { // Allow small reduction
            hexVerticesX[i] = candidateX;
            hexVerticesY[i] = candidateY;
        }
    }

} else {
    // === HIGH-PRECISION ADAPTIVE FITTING APPROACH ===
    print("Using high-precision adaptive fitting for irregular shapes...");

    // STAGE 1: Enhanced corner detection with multiple curvature windows
    print("Stage 1: Multi-scale corner detection...");

    allCorners = newArray();
    allCornerScores = newArray();
    cornerCount = 0;

    // Test multiple curvature window sizes for robustness
    curvatureWindows = newArray(1, 2, 3);

    for (windowIdx = 0; windowIdx < curvatureWindows.length; windowIdx++) {
        CURVATURE_WINDOW = curvatureWindows[windowIdx];
        curvatures = newArray(nHullPoints);

        for (i = 0; i < nHullPoints; i++) {
            // Get points for curvature calculation
            prev = (i - CURVATURE_WINDOW + nHullPoints) % nHullPoints;
            next = (i + CURVATURE_WINDOW) % nHullPoints;

            // Calculate vectors
            v1x = hullX[i] - hullX[prev];
            v1y = hullY[i] - hullY[prev];
            v2x = hullX[next] - hullX[i];
            v2y = hullY[next] - hullY[i];

            // Calculate angle change (curvature)
            angle1 = atan2(v1y, v1x);
            angle2 = atan2(v2y, v2x);
            angleDiff = angle2 - angle1;

            // Normalize angle difference to [-π, π]
            while (angleDiff > PI) angleDiff -= 2 * PI;
            while (angleDiff < -PI) angleDiff += 2 * PI;

            curvatures[i] = abs(angleDiff);
        }

        // Find corner candidates (local maxima in curvature)
        CURVATURE_THRESHOLD = 0.2; // Lower threshold for better detection

        for (i = 0; i < nHullPoints; i++) {
            if (curvatures[i] > CURVATURE_THRESHOLD) {
                // Check if this is a local maximum
                prev = (i - 1 + nHullPoints) % nHullPoints;
                next = (i + 1) % nHullPoints;

                if (curvatures[i] >= curvatures[prev] && curvatures[i] >= curvatures[next]) {
                    // Check if this corner is already detected
                    isNewCorner = true;
                    for (j = 0; j < cornerCount; j++) {
                        if (abs(allCorners[j] - i) <= 2) { // Within 2 points
                            // Update score if this is better
                            if (curvatures[i] > allCornerScores[j]) {
                                allCorners[j] = i;
                                allCornerScores[j] = curvatures[i];
                            }
                            isNewCorner = false;
                            break;
                        }
                    }

                    if (isNewCorner) {
                        allCorners = Array.concat(allCorners, i);
                        allCornerScores = Array.concat(allCornerScores, curvatures[i]);
                        cornerCount++;
                    }
                }
            }
        }
    }

    print("Found " + cornerCount + " corner candidates across multiple scales");

    // STAGE 2: Select and optimize 6 best corners
    if (cornerCount >= 6) {
        // Sort corners by curvature strength and select top 6
        for (i = 0; i < cornerCount - 1; i++) {
            for (j = i + 1; j < cornerCount; j++) {
                if (allCornerScores[i] < allCornerScores[j]) {
                    temp = allCorners[i];
                    allCorners[i] = allCorners[j];
                    allCorners[j] = temp;
                    temp = allCornerScores[i];
                    allCornerScores[i] = allCornerScores[j];
                    allCornerScores[j] = temp;
                }
            }
        }

        // Take top 6 corners and sort by angle
        selectedCorners = newArray(6);
        for (i = 0; i < 6; i++) {
            selectedCorners[i] = allCorners[i];
        }

        // Sort selected corners by angle around centroid
        for (i = 0; i < 5; i++) {
            for (j = i + 1; j < 6; j++) {
                angle_i = atan2(hullY[selectedCorners[i]] - centroidY, hullX[selectedCorners[i]] - centroidX);
                angle_j = atan2(hullY[selectedCorners[j]] - centroidY, hullX[selectedCorners[j]] - centroidX);
                if (angle_i > angle_j) {
                    temp = selectedCorners[i];
                    selectedCorners[i] = selectedCorners[j];
                    selectedCorners[j] = temp;
                }
            }
        }

        // Assign initial vertices
        for (i = 0; i < 6; i++) {
            hexVerticesX[i] = hullX[selectedCorners[i]];
            hexVerticesY[i] = hullY[selectedCorners[i]];
        }

        print("Using corner-based vertex detection");

        // STAGE 3: Weighted fine-tuning with angle constraints
        print("Stage 2: Constrained fine-tuning with regularity weighting...");

        // STAGE 3a: Optimize centroid position for adaptive fitting
        print("Stage 3a: Optimizing centroid position for adaptive fitting...");

        bestCentroidX = centroidX;
        bestCentroidY = centroidY;
        bestCentroidScore = -999999;

        // Test different centroid positions
        for (dx = -CENTROID_OPTIMIZATION_RANGE; dx <= CENTROID_OPTIMIZATION_RANGE; dx += CENTROID_OPTIMIZATION_STEP) {
            for (dy = -CENTROID_OPTIMIZATION_RANGE; dy <= CENTROID_OPTIMIZATION_RANGE; dy += CENTROID_OPTIMIZATION_STEP) {
                testCentroidX = centroidX + dx;
                testCentroidY = centroidY + dy;

                // Calculate how well the current vertices work with this centroid
                containmentCount = 0;
                for (k = 0; k < nHullPoints; k++) {
                    if (isPointInPolygon(hullX[k], hullY[k], hexVerticesX, hexVerticesY, 6)) {
                        containmentCount++;
                    }
                }

                // Also consider distance from centroid to vertices (regularity)
                avgDistanceFromCentroid = 0;
                for (i = 0; i < 6; i++) {
                    dist = sqrt((hexVerticesX[i] - testCentroidX) * (hexVerticesX[i] - testCentroidX) +
                               (hexVerticesY[i] - testCentroidY) * (hexVerticesY[i] - testCentroidY));
                    avgDistanceFromCentroid += dist;
                }
                avgDistanceFromCentroid /= 6;

                // Score combines containment and regularity
                score = containmentCount * 100 - abs(avgDistanceFromCentroid - avgRadius) * 10;

                if (score > bestCentroidScore) {
                    bestCentroidScore = score;
                    bestCentroidX = testCentroidX;
                    bestCentroidY = testCentroidY;
                }
            }
        }

        // Update centroid if a better position was found
        if (bestCentroidX != centroidX || bestCentroidY != centroidY) {
            print("Adaptive centroid optimized: moved by (" + d2s(bestCentroidX - centroidX, 1) + ", " + d2s(bestCentroidY - centroidY, 1) + ") pixels");
            centroidX = bestCentroidX;
            centroidY = bestCentroidY;
        }

        // Calculate reference regular hexagon for weighting
        referenceVerticesX = newArray(6);
        referenceVerticesY = newArray(6);

        // Find best orientation for reference hexagon with optimized centroid
        bestRefScore = -999999;
        bestRefRotation = 0;

        for (testRotation = 0; testRotation < 360; testRotation += 5) {
            score = calculateContainmentScore(testRotation, centroidX, centroidY, avgRadius, hullX, hullY, nHullPoints);
            if (score > bestRefScore) {
                bestRefScore = score;
                bestRefRotation = testRotation;
            }
        }

        for (i = 0; i < 6; i++) {
            angle = (i * 60 + bestRefRotation) * PI / 180;
            referenceVerticesX[i] = centroidX + avgRadius * cos(angle);
            referenceVerticesY[i] = centroidY + avgRadius * sin(angle);
        }

        // Iterative weighted adjustment
        for (iteration = 0; iteration < 8; iteration++) {
            improved = false;

            for (vertexIdx = 0; vertexIdx < 6; vertexIdx++) {
                bestX = hexVerticesX[vertexIdx];
                bestY = hexVerticesY[vertexIdx];
                bestScore = calculateWeightedScore(hexVerticesX, hexVerticesY, referenceVerticesX, referenceVerticesY,
                                                  hullX, hullY, nHullPoints, currentRegularityWeight);

                // Test small adjustments around current position
                for (dx = -1.5; dx <= 1.5; dx += 0.5) {
                    for (dy = -1.5; dy <= 1.5; dy += 0.5) {
                        if (dx == 0 && dy == 0) continue;

                        // Test new position
                        testVerticesX = Array.copy(hexVerticesX);
                        testVerticesY = Array.copy(hexVerticesY);
                        testVerticesX[vertexIdx] += dx;
                        testVerticesY[vertexIdx] += dy;

                        // Check angle constraints
                        if (isAngleConstraintViolated(testVerticesX, testVerticesY, vertexIdx)) {
                            continue; // Skip if angle deviation is too large
                        }

                        // Calculate weighted score
                        score = calculateWeightedScore(testVerticesX, testVerticesY, referenceVerticesX, referenceVerticesY,
                                                      hullX, hullY, nHullPoints, currentRegularityWeight);

                        if (score > bestScore) {
                            bestScore = score;
                            bestX = testVerticesX[vertexIdx];
                            bestY = testVerticesY[vertexIdx];
                            improved = true;
                        }
                    }
                }

                hexVerticesX[vertexIdx] = bestX;
                hexVerticesY[vertexIdx] = bestY;
            }

            if (!improved) break;
        }

    } else {
        // STAGE 2 (Fallback): High-precision sector-based approach
        print("Insufficient corners found, using high-precision sector-based approach");

        sectorSize = 360.0 / 6.0;

        for (sector = 0; sector < 6; sector++) {
            sectorStart = sector * sectorSize;
            sectorEnd = (sector + 1) * sectorSize;

            // Find all points in this sector and choose the optimal one
            sectorPoints = newArray();
            sectorDistances = newArray();
            pointsInSector = 0;

            for (i = 0; i < nHullPoints; i++) {
                angle = angles[i];
                // Handle wrap-around at 360°
                if (sector == 5 && angle < sectorSize) angle += 360;

                if (angle >= sectorStart && angle < sectorEnd) {
                    sectorPoints = Array.concat(sectorPoints, i);
                    sectorDistances = Array.concat(sectorDistances, distances[i]);
                    pointsInSector++;
                }
            }

            if (pointsInSector > 0) {
                // Choose point that maximizes containment
                bestPointIdx = 0;
                bestContainment = 0;

                for (i = 0; i < pointsInSector; i++) {
                    pointIdx = sectorPoints[i];

                    // Test this point as vertex
                    testVerticesX = newArray(6);
                    testVerticesY = newArray(6);

                    for (j = 0; j < 6; j++) {
                        if (j == sector) {
                            testVerticesX[j] = hullX[pointIdx];
                            testVerticesY[j] = hullY[pointIdx];
                        } else {
                            // Use average radius for other vertices
                            testAngle = (j * 60) * PI / 180;
                            testVerticesX[j] = centroidX + avgRadius * cos(testAngle);
                            testVerticesY[j] = centroidY + avgRadius * sin(testAngle);
                        }
                    }

                    // Count containment
                    containment = 0;
                    for (k = 0; k < nHullPoints; k++) {
                        if (isPointInPolygon(hullX[k], hullY[k], testVerticesX, testVerticesY, 6)) {
                            containment++;
                        }
                    }

                    if (containment > bestContainment) {
                        bestContainment = containment;
                        bestPointIdx = pointIdx;
                    }
                }

                hexVerticesX[sector] = hullX[bestPointIdx];
                hexVerticesY[sector] = hullY[bestPointIdx];

            } else {
                // No points in sector, interpolate
                targetAngle = (sectorStart + sectorEnd) / 2 * PI / 180;
                hexVerticesX[sector] = centroidX + avgRadius * cos(targetAngle);
                hexVerticesY[sector] = centroidY + avgRadius * sin(targetAngle);
            }
        }
    }
}

if (useRegularityConstraint) {
    print("Hexagon vertices identified using regularity-weighted approach");
} else {
    print("Hexagon vertices identified using adaptive fitting approach");
}

// === CREATE FITTED HEXAGON ===
// Create polygon from vertices
makeSelection("polygon", hexVerticesX, hexVerticesY);
fittedArea = getValue("Area");

print("Fitted hexagon area: " + fittedArea + " pixels²");

// === ANGLE VALIDATION ===
print("\n--- Angle Validation ---");

validHexagon = true;
angleErrors = newArray(6);

for (i = 0; i < 6; i++) {
    // Calculate angle between three consecutive vertices
    p1 = i;
    p2 = (i + 1) % 6;
    p3 = (i + 2) % 6;
    
    // Vectors from p2 to p1 and p2 to p3
    v1x = hexVerticesX[p1] - hexVerticesX[p2];
    v1y = hexVerticesY[p1] - hexVerticesY[p2];
    v2x = hexVerticesX[p3] - hexVerticesX[p2];
    v2y = hexVerticesY[p3] - hexVerticesY[p2];
    
    // Calculate angle using dot product
    dot = v1x * v2x + v1y * v2y;
    mag1 = sqrt(v1x * v1x + v1y * v1y);
    mag2 = sqrt(v2x * v2x + v2y * v2y);
    
    if (mag1 > 0 && mag2 > 0) {
        cosAngle = dot / (mag1 * mag2);
        // Clamp to valid range for acos
        if (cosAngle > 1) cosAngle = 1;
        if (cosAngle < -1) cosAngle = -1;
        angle = acos(cosAngle) * 180 / PI;
        
        // Interior angle of hexagon should be 120°
        angleError = abs(angle - 120);
        angleErrors[i] = angleError;
        
        print("Angle " + (i+1) + ": " + d2s(angle, 1) + "° (error: " + d2s(angleError, 1) + "°)");
        
        if (angleError > ANGLE_TOLERANCE) {
            validHexagon = false;
        }
    }
}

// === PARALLEL LINE VALIDATION ===
print("\n--- Parallel Line Validation ---");

for (i = 0; i < 3; i++) {
    // Check if opposing sides are parallel
    side1_start = i;
    side1_end = (i + 1) % 6;
    side2_start = (i + 3) % 6;
    side2_end = (i + 4) % 6;
    
    // Calculate slopes
    dx1 = hexVerticesX[side1_end] - hexVerticesX[side1_start];
    dy1 = hexVerticesY[side1_end] - hexVerticesY[side1_start];
    dx2 = hexVerticesX[side2_end] - hexVerticesX[side2_start];
    dy2 = hexVerticesY[side2_end] - hexVerticesY[side2_start];
    
    // Calculate angles of the lines
    angle1 = atan2(dy1, dx1) * 180 / PI;
    angle2 = atan2(dy2, dx2) * 180 / PI;
    
    // Normalize angles to [0, 180)
    if (angle1 < 0) angle1 += 180;
    if (angle2 < 0) angle2 += 180;
    
    // Calculate difference (should be close to 0° for parallel lines)
    angleDiff = abs(angle1 - angle2);
    if (angleDiff > 90) angleDiff = 180 - angleDiff;
    
    print("Opposing sides " + (i+1) + ": angle difference = " + d2s(angleDiff, 1) + "°");
    
    if (angleDiff > PARALLEL_TOLERANCE) {
        validHexagon = false;
    }
}

// === RESULTS ===
print("\n--- Results ---");

areaDifference = abs(fittedArea - originalArea);
areaRatio = fittedArea / originalArea;

print("Original ROI area: " + d2s(originalArea, 2) + " pixels²");
print("Fitted hexagon area: " + d2s(fittedArea, 2) + " pixels²");
print("Area difference: " + d2s(areaDifference, 2) + " pixels²");
print("Area ratio (fitted/original): " + d2s(areaRatio, 3));

if (validHexagon) {
    print("✓ Hexagon validation: PASSED");
} else {
    print("✗ Hexagon validation: FAILED (angles or parallelism outside tolerance)");
}

// === CLEAN VISUALIZATION ===
print("\n--- Final Visualization ---");

// Clear any remaining overlays
run("Remove Overlay");

// Clear ROI Manager and add only the fitted hexagon
roiManager("Reset");
roiManager("Add");
roiManager("Select", 0);
roiManager("Rename", "Fitted_Hexagon");

// Show only the fitted hexagon
roiManager("Show All with labels");
roiManager("Deselect");

// === FINAL HEXAGON DIRECTIONAL OPTIMIZATION ===
print("\n--- Directional Hexagon Optimization ---");

// Calculate current hexagon orientation for directional shifts
print("Testing directional shifts parallel and perpendicular to hexagon sides...");

bestFinalScore = -999999;
bestShiftX = 0;
bestShiftY = 0;
originalCentroidX = centroidX;
originalCentroidY = centroidY;

// Calculate the current hexagon's orientation from its vertices
// Use the first side to determine the base orientation
side1X = hexVerticesX[1] - hexVerticesX[0];
side1Y = hexVerticesY[1] - hexVerticesY[0];
baseOrientation = atan2(side1Y, side1X) * 180 / PI;

// Define 12 test directions:
// 6 directions parallel to hexagon sides (every 60°)
// 6 directions perpendicular to hexagon sides (every 60° + 30° offset)
testDirections = newArray(12);
for (i = 0; i < 6; i++) {
    // Parallel to sides
    testDirections[i] = baseOrientation + i * 60;
    // Perpendicular to sides (30° offset)
    testDirections[i + 6] = baseOrientation + i * 60 + 30;
}

// Test shifts in each direction
SHIFT_RANGE = 2.0;  // Maximum shift distance in pixels
SHIFT_STEP = 0.25;  // Step size for testing

for (dirIdx = 0; dirIdx < 12; dirIdx++) {
    direction = testDirections[dirIdx] * PI / 180;  // Convert to radians
    directionX = cos(direction);
    directionY = sin(direction);

    // Test different shift magnitudes in this direction
    for (shiftMagnitude = -SHIFT_RANGE; shiftMagnitude <= SHIFT_RANGE; shiftMagnitude += SHIFT_STEP) {
        if (shiftMagnitude == 0) continue;

        // Calculate shift components
        shiftX = shiftMagnitude * directionX;
        shiftY = shiftMagnitude * directionY;

        // Create test hexagon with shifted vertices
        testHexX = newArray(6);
        testHexY = newArray(6);

        for (i = 0; i < 6; i++) {
            testHexX[i] = hexVerticesX[i] + shiftX;
            testHexY[i] = hexVerticesY[i] + shiftY;
        }

        // Calculate comprehensive score for this shift
        score = calculateDirectionalShiftScore(testHexX, testHexY, hullX, hullY, nHullPoints,
                                             originalCentroidX, originalCentroidY, dirIdx);

        if (score > bestFinalScore) {
            bestFinalScore = score;
            bestShiftX = shiftX;
            bestShiftY = shiftY;
        }
    }
}

// Apply best directional shift if it improves the fit
shiftMagnitude = sqrt(bestShiftX * bestShiftX + bestShiftY * bestShiftY);
if (shiftMagnitude > 0.1) {
    shiftDirection = atan2(bestShiftY, bestShiftX) * 180 / PI;
    print("Applying directional shift: " + d2s(shiftMagnitude, 2) + " pixels at " + d2s(shiftDirection, 1) + "°");

    for (i = 0; i < 6; i++) {
        hexVerticesX[i] += bestShiftX;
        hexVerticesY[i] += bestShiftY;
    }

    // Update centroid
    centroidX += bestShiftX;
    centroidY += bestShiftY;

    // Verify final containment
    finalInsideCount = 0;
    for (i = 0; i < nHullPoints; i++) {
        if (isPointInPolygon(hullX[i], hullY[i], hexVerticesX, hexVerticesY, 6)) {
            finalInsideCount++;
        }
    }

    finalContainment = finalInsideCount / nHullPoints;
    print("Final containment after directional shift: " + d2s(finalContainment * 100, 1) + "%");
} else {
    print("No beneficial directional shift found");
}

print("Displaying fitted hexagon only");

// === SUMMARY ===
print("\n=== Analysis Complete ===");
print("Hexagon fitting completed successfully.");
print("Check the ROI Manager for the fitted hexagon overlay.");

// Clean up detection image
selectImage(detectionID);
close();

selectImage(original);

// === HELPER FUNCTIONS ===

function calculateContainmentScore(rotation, centerX, centerY, radius, hullPointsX, hullPointsY, numPoints) {
    // Generate hexagon vertices at given rotation
    verticesX = newArray(6);
    verticesY = newArray(6);

    for (i = 0; i < 6; i++) {
        angle = (i * 60 + rotation) * PI / 180;
        verticesX[i] = centerX + radius * cos(angle);
        verticesY[i] = centerY + radius * sin(angle);
    }

    // Count points inside hexagon
    insideCount = 0;
    totalFitDistance = 0;

    for (i = 0; i < numPoints; i++) {
        if (isPointInPolygon(hullPointsX[i], hullPointsY[i], verticesX, verticesY, 6)) {
            insideCount++;
        }

        // Also calculate fit quality (distance to nearest vertex)
        minVertexDist = 999999;
        for (j = 0; j < 6; j++) {
            dist = sqrt((hullPointsX[i] - verticesX[j]) * (hullPointsX[i] - verticesX[j]) +
                       (hullPointsY[i] - verticesY[j]) * (hullPointsY[i] - verticesY[j]));
            if (dist < minVertexDist) {
                minVertexDist = dist;
            }
        }
        totalFitDistance += minVertexDist;
    }

    // Score combines containment and fit quality
    containmentRatio = insideCount / numPoints;
    avgFitDistance = totalFitDistance / numPoints;

    // Heavily weight containment, moderately weight fit
    score = containmentRatio * 1000 - avgFitDistance * DEVIATION_PENALTY_FACTOR;

    return score;
}

function isPointInPolygon(px, py, polyX, polyY, numVertices) {
    // Ray casting algorithm for point-in-polygon test
    inside = false;

    j = numVertices - 1;
    for (i = 0; i < numVertices; i++) {
        xi = polyX[i];
        yi = polyY[i];
        xj = polyX[j];
        yj = polyY[j];

        if (((yi > py) != (yj > py)) && (px < (xj - xi) * (py - yi) / (yj - yi) + xi)) {
            inside = !inside;
        }
        j = i;
    }

    return inside;
}

function calculateWeightedScore(actualVerticesX, actualVerticesY, referenceVerticesX, referenceVerticesY,
                               hullPointsX, hullPointsY, numPoints, regularityWeight) {
    // Calculate containment score
    insideCount = 0;
    for (i = 0; i < numPoints; i++) {
        if (isPointInPolygon(hullPointsX[i], hullPointsY[i], actualVerticesX, actualVerticesY, 6)) {
            insideCount++;
        }
    }
    containmentScore = insideCount / numPoints * 1000; // Scale up containment importance

    // Calculate regularity score (distance from reference regular hexagon)
    regularityScore = 0;
    for (i = 0; i < 6; i++) {
        dist = sqrt((actualVerticesX[i] - referenceVerticesX[i]) * (actualVerticesX[i] - referenceVerticesX[i]) +
                   (actualVerticesY[i] - referenceVerticesY[i]) * (actualVerticesY[i] - referenceVerticesY[i]));
        regularityScore -= dist; // Negative because closer to reference is better
    }

    // Weighted combination
    totalScore = containmentScore + regularityWeight * regularityScore;

    return totalScore;
}

function isAngleConstraintViolated(verticesX, verticesY, changedVertexIdx) {
    // Check if any interior angle deviates more than 5° from 120° (very strict)
    MAX_ANGLE_DEVIATION = 15; // Degrees

    for (i = 0; i < 6; i++) {
        // Calculate interior angle at vertex i
        prev = (i - 1 + 6) % 6;
        next = (i + 1) % 6;

        // Vectors from current vertex to adjacent vertices
        v1x = verticesX[prev] - verticesX[i];
        v1y = verticesY[prev] - verticesY[i];
        v2x = verticesX[next] - verticesX[i];
        v2y = verticesY[next] - verticesY[i];

        // Calculate angle using atan2 for better numerical stability
        angle1 = atan2(v1y, v1x);
        angle2 = atan2(v2y, v2x);

        // Calculate interior angle
        angleDiff = angle2 - angle1;

        // Normalize to [0, 2π]
        while (angleDiff < 0) angleDiff += 2 * PI;
        while (angleDiff >= 2 * PI) angleDiff -= 2 * PI;

        // Convert to degrees
        interiorAngle = angleDiff * 180 / PI;

        // For a convex hexagon, we want the smaller angle (interior angle)
        if (interiorAngle > 180) {
            interiorAngle = 360 - interiorAngle;
        }

        // Check deviation from 120°
        angleError = abs(interiorAngle - 120);

        if (angleError > MAX_ANGLE_DEVIATION) {
            return true; // Constraint violated
        }
    }

    return false; // All angles within acceptable range
}

function calculateDirectionalShiftScore(testVerticesX, testVerticesY, hullPointsX, hullPointsY, numPoints,
                                       originalCenterX, originalCenterY, directionIndex) {
    // Calculate containment score
    insideCount = 0;
    outsidePoints = newArray();
    outsideCount = 0;

    for (i = 0; i < numPoints; i++) {
        if (isPointInPolygon(hullPointsX[i], hullPointsY[i], testVerticesX, testVerticesY, 6)) {
            insideCount++;
        } else {
            outsidePoints = Array.concat(outsidePoints, i);
            outsideCount++;
        }
    }

    containmentRatio = insideCount / numPoints;

    // Calculate balance score based on distribution of outside points
    // Analyze where the outside points are relative to the hexagon center
    balanceScore = 0;
    if (outsideCount > 0) {
        // Calculate center of test hexagon
        testCenterX = 0;
        testCenterY = 0;
        for (i = 0; i < 6; i++) {
            testCenterX += testVerticesX[i];
            testCenterY += testVerticesY[i];
        }
        testCenterX /= 6;
        testCenterY /= 6;

        // Analyze distribution of outside points in 6 sectors around hexagon
        sectorCounts = newArray(6);
        for (i = 0; i < 6; i++) {
            sectorCounts[i] = 0;
        }

        for (i = 0; i < outsideCount; i++) {
            pointIdx = outsidePoints[i];
            // Calculate angle from hexagon center to outside point
            dx = hullPointsX[pointIdx] - testCenterX;
            dy = hullPointsY[pointIdx] - testCenterY;
            angle = atan2(dy, dx) * 180 / PI;
            if (angle < 0) angle += 360;

            // Determine which sector (0-5) this point falls into
            sector = floor(angle / 60) % 6;
            sectorCounts[sector]++;
        }

        // Calculate balance as negative variance of sector counts
        // Lower variance = better balance = higher score
        avgSectorCount = outsideCount / 6.0;
        variance = 0;
        for (i = 0; i < 6; i++) {
            diff = sectorCounts[i] - avgSectorCount;
            variance += diff * diff;
        }
        variance /= 6;
        balanceScore = -variance; // Negative because lower variance is better
    }

    // Calculate fit quality score (how close vertices are to hull points)
    fitScore = 0;
    for (i = 0; i < 6; i++) {
        minDistToHull = 999999;
        for (j = 0; j < numPoints; j++) {
            dist = sqrt((testVerticesX[i] - hullPointsX[j]) * (testVerticesX[i] - hullPointsX[j]) +
                       (testVerticesY[i] - hullPointsY[j]) * (testVerticesY[i] - hullPointsY[j]));
            if (dist < minDistToHull) {
                minDistToHull = dist;
            }
        }
        fitScore -= minDistToHull; // Negative because smaller distances are better
    }

    // Combine scores with appropriate weights
    // Heavily weight containment, moderately weight balance and fit
    totalScore = containmentRatio * 1000 + balanceScore * 20 + fitScore * 5;

    return totalScore;
}
